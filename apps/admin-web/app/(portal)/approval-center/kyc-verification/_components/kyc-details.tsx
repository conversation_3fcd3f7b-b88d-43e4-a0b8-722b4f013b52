import { Text, Separator, Button } from "@radix-ui/themes";
import { CopyButton } from "@/ui-components/copy-button";
import { KycStatusBadge } from "./kyc-status-badge";
import { MediaButton } from "./media-dialog";
import { KycAuditLogTable } from "./kyc-audit-log-table";
import type { KycSubmission } from "./kyc-review/context";
import type { KycVerificationSearchResponse } from "@/api/data-contracts";

interface DetailRowProps {
  label: string;
  value: string | number | React.ReactNode;
  enableCopy?: boolean;
  copyText?: string;
}

const DetailRow = ({ label, value, enableCopy, copyText }: DetailRowProps) => {
  return (
    <div className="flex flex-col gap-1 items-start">
      <Text size="2" color="gray">
        {label}
      </Text>
      <div className="flex gap-2 items-center min-w-0 flex-1 justify-end">
        <Text size="2">{value}</Text>
        {enableCopy && copyText && <CopyButton text={copyText} />}
      </div>
    </div>
  );
};

export const KycDetailsHeader = ({
  data,
  onKycReview,
}: {
  data: KycVerificationSearchResponse;
  onKycReview: (submission: KycSubmission | null) => void;
}) => {
  const { submissionId, email, publicId, status } = data;

  if (!submissionId) {
    return null;
  }

  const isPending = status === "PENDING";

  const handleReject = () => {
    onKycReview({ submissionId, action: "REJECT" });
  };

  const handleRestrict = () => {
    onKycReview({ submissionId, action: "RESTRICT" });
  };

  const handleApprove = () => {
    onKycReview({ submissionId, action: "APPROVE" });
  };

  return (
    <div className="flex gap-6 justify-between">
      <div>
        <div className="grow flex items-center gap-2 mb-1">
          <Text size="4" weight="bold">
            {email}
          </Text>
          <KycStatusBadge status={status} />
        </div>
        <Text size="1" color="gray" weight="light" highContrast>
          Portfolio ID: {publicId}
        </Text>
      </div>
      {isPending && (
        <div className="flex gap-3">
          <Button
            size="1"
            variant="soft"
            radius="full"
            color="red"
            highContrast
            onClick={handleRestrict}
          >
            Restrict
          </Button>
          <Button
            size="1"
            variant="soft"
            radius="full"
            color="red"
            highContrast
            onClick={handleReject}
          >
            Reject
          </Button>
          <Button
            size="1"
            variant="soft"
            radius="full"
            color="gray"
            highContrast
            onClick={handleApprove}
          >
            Approve
          </Button>
        </div>
      )}
    </div>
  );
};

const formatDate = (dateString?: string) => {
  if (!dateString) return "-";
  const date = new Date(dateString);
  return date.toLocaleDateString("en-GB", {
    day: "2-digit",
    month: "2-digit",
    year: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit",
    hour12: false,
  });
};

const renderBasicInfo = (data: KycVerificationSearchResponse) => {
  const {
    submissionId,
    email,
    firstName,
    lastName,
    nationality,
    countryOfResidence,
    identityDocumentType,
    submissionDate,
  } = data;

  const displayDocumentType = identityDocumentType
    ?.replace(/_/g, "/")
    .toLowerCase()
    .replace(/\b\w/g, (l) => l.toUpperCase());

  return (
    <div className="grid grid-cols-4 gap-y-4 gap-x-6">
      <DetailRow
        label="Submission ID"
        value={submissionId || "-"}
        enableCopy={!!submissionId}
        copyText={submissionId?.toString()}
      />
      <DetailRow
        label="Email"
        value={email || "-"}
        enableCopy={!!email}
        copyText={email}
      />
      <DetailRow label="First name" value={firstName || "-"} />
      <DetailRow label="Last name" value={lastName || "-"} />
      <DetailRow label="Nationality" value={nationality || "-"} />
      <DetailRow label="Country of residence" value={countryOfResidence || "-"} />
      <DetailRow label="ID type" value={displayDocumentType || "-"} />
      <DetailRow label="Submitted date" value={formatDate(submissionDate)} />
    </div>
  );
};

const renderDocuments = (data: KycVerificationSearchResponse) => {
  const { documentFrontKey, documentBackKey, selfieKey, identityDocumentType } = data;

  const displayDocumentType = identityDocumentType
    ?.replace(/_/g, "/")
    .toLowerCase()
    .replace(/\b\w/g, (l) => l.toUpperCase());

  return (
    <>
      <Separator size="4" />
      <div className="flex flex-col gap-4">
        <Text size="3" weight="medium">
          Submitted documents
        </Text>
        
        <div className="flex flex-col gap-2">
          <Text size="2" color="gray">
            ID type
          </Text>
          <Text size="2">{displayDocumentType || "-"}</Text>
        </div>

        <div className="flex flex-col gap-2">
          <Text size="2" color="gray">
            Uploaded documents
          </Text>
          <div className="flex gap-2">
            {documentFrontKey && (
              <MediaButton
                title="Image"
                url={documentFrontKey}
                type="image"
              />
            )}
            {documentBackKey && (
              <MediaButton
                title="Image"
                url={documentBackKey}
                type="image"
              />
            )}
          </div>
        </div>

        <div className="flex flex-col gap-2">
          <Text size="2" color="gray">
            Selfie image
          </Text>
          {selfieKey ? (
            <MediaButton
              title="Image"
              url={selfieKey}
              type="image"
            />
          ) : (
            <Text size="2">-</Text>
          )}
        </div>
      </div>
    </>
  );
};

export const KycDetails = ({ data }: { data: KycVerificationSearchResponse }) => {
  return (
    <div className="space-y-8">
      {/* Basic Information */}
      <div className="flex flex-col gap-2">
        <Text size="3" weight="medium">
          Basic information
        </Text>
        {renderBasicInfo(data)}
      </div>

      {renderDocuments(data)}
      <Separator size="4" />

      {/* Audit Log */}
      <div className="flex flex-col gap-2">
        <Text size="3" weight="medium">
          Audit log
        </Text>
        <KycAuditLogTable data={data} />
      </div>
    </div>
  );
};

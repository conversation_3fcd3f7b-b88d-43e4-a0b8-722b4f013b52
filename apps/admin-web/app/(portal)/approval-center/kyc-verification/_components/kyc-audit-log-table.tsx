import { Table, Text } from "@radix-ui/themes";
import { KycVerificationSearchResponse } from "@/api/data-contracts";
import { KycStatusBadge } from "./kyc-status-badge";

interface KycAuditLogTableProps {
  data: KycVerificationSearchResponse;
}

const formatDate = (dateString?: string) => {
  if (!dateString) return "-";
  const date = new Date(dateString);
  return date.toLocaleDateString("en-GB", {
    day: "2-digit",
    month: "2-digit",
    year: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit",
    hour12: false,
  });
};

export const KycAuditLogTable = ({ data }: KycAuditLogTableProps) => {
  const { status, operatorName, lastUpdated, actionReason } = data;

  // Only show audit log if there's a status change (not PENDING)
  const hasAuditRecord = status !== "PENDING" && lastUpdated;

  if (!hasAuditRecord) {
    return (
      <div className="bg-gray-50 rounded-lg p-8 text-center">
        <div className="flex flex-col items-center gap-4">
          <div className="w-16 h-16 bg-orange-200 rounded-lg flex items-center justify-center">
            <svg
              width="32"
              height="32"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M3 7V17C3 18.1046 3.89543 19 5 19H19C20.1046 19 21 18.1046 21 17V7C21 5.89543 20.1046 5 19 5H5C3.89543 5 3 5.89543 3 7Z"
                stroke="#F97316"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <path
                d="M3 7L12 13L21 7"
                stroke="#F97316"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </div>
          <Text size="3" weight="medium" color="gray">
            No records found
          </Text>
        </div>
      </div>
    );
  }

  const displayReason = actionReason
    ?.replace(/_/g, " ")
    .toLowerCase()
    .replace(/\b\w/g, (l) => l.toUpperCase());

  return (
    <Table.Root variant="surface" size="2">
      <Table.Header>
        <Table.Row>
          <Table.ColumnHeaderCell>Status</Table.ColumnHeaderCell>
          <Table.ColumnHeaderCell>Audit reason</Table.ColumnHeaderCell>
          <Table.ColumnHeaderCell>Audit user</Table.ColumnHeaderCell>
          <Table.ColumnHeaderCell>Audit time</Table.ColumnHeaderCell>
        </Table.Row>
      </Table.Header>
      <Table.Body>
        <Table.Row>
          <Table.Cell>
            <KycStatusBadge status={status} />
          </Table.Cell>
          <Table.Cell>
            <Text size="2">{displayReason || "-"}</Text>
          </Table.Cell>
          <Table.Cell>
            <Text size="2">{operatorName || "{user}"}</Text>
          </Table.Cell>
          <Table.Cell>
            <Text size="2">{formatDate(lastUpdated)}</Text>
          </Table.Cell>
        </Table.Row>
      </Table.Body>
    </Table.Root>
  );
};
